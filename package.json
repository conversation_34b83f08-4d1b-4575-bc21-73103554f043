{"name": "opencode", "main": "expo-router/entry", "version": "1.0.0", "scripts": {"start": "expo start", "reset-project": "node ./scripts/reset-project.js", "android": "expo run:android", "ios": "expo run:ios", "web": "expo start --web", "lint": "expo lint", "format": "prettier --write .", "format:check": "prettier --check ."}, "dependencies": {"@expo/vector-icons": "^14.1.0", "@legendapp/list": "^1.1.4", "@legendapp/state": "^3.0.0-beta.15", "@opencode-ai/sdk": "^0.5.12", "@react-native-clipboard/clipboard": "1.16.3", "@react-navigation/bottom-tabs": "^7.3.10", "@react-navigation/elements": "^2.3.8", "@react-navigation/native": "^7.1.6", "@tamagui/animations-react-native": "1.132.20", "@tamagui/babel-plugin": "1.132.20", "@tamagui/button": "1.132.20", "@tamagui/card": "1.132.20", "@tamagui/config": "1.132.20", "@tamagui/core": "1.132.20", "@tamagui/input": "1.132.20", "@tamagui/lucide-icons": "1.132.20", "@tamagui/portal": "1.132.20", "@tamagui/radio-group": "1.132.20", "@tamagui/sheet": "1.132.20", "@tamagui/stacks": "1.132.20", "@tamagui/text": "1.132.20", "@tamagui/toast": "1.132.20", "burnt": "0.13.0", "diff": "8.0.2", "expo": "~53.0.17", "expo-blur": "~14.1.5", "expo-constants": "~17.1.7", "expo-dev-client": "~5.2.4", "expo-font": "~13.3.2", "expo-haptics": "14.1.4", "expo-image": "~2.4.0", "expo-linking": "~7.1.7", "expo-router": "~5.1.3", "expo-splash-screen": "~0.30.10", "expo-status-bar": "~2.2.3", "expo-symbols": "~0.4.5", "expo-web-browser": "~14.2.0", "react": "19.0.0", "react-dom": "19.0.0", "react-native": "0.79.5", "react-native-code-highlighter": "1.3.0", "react-native-gesture-handler": "~2.24.0", "react-native-markdown-display": "^7.0.2", "react-native-mmkv": "3.3.0", "react-native-reanimated": "~3.17.4", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.11.1", "react-native-sse": "^1.2.1", "react-native-svg": "15.11.2", "react-native-svg-transformer": "1.5.1", "react-native-url-polyfill": "^2.0.0", "react-native-web": "~0.20.0", "react-native-webview": "13.13.5", "react-syntax-highlighter": "15.6.1", "tamagui": "1.132.20"}, "devDependencies": {"@babel/core": "^7.25.2", "@tamagui/metro-plugin": "^1.132.20", "@types/react": "~19.0.10", "@types/react-syntax-highlighter": "15.5.13", "eslint": "^9.25.0", "eslint-config-expo": "~9.2.0", "metro": "^0.82.5", "metro-config": "^0.82.5", "metro-transform-worker": "^0.82.5", "patch-package": "^8.0.0", "prettier": "3.6.2", "typescript": "~5.8.3"}, "private": true}