{"name": "opencode", "type": "module", "version": "1.0.0", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "format": "prettier --write .", "format:check": "prettier --check ."}, "dependencies": {"@legendapp/list": "^1.1.4", "@legendapp/state": "^3.0.0-beta.15", "@opencode-ai/sdk": "^0.5.12", "@tamagui/animations-css": "1.132.20", "@tamagui/button": "1.132.20", "@tamagui/card": "1.132.20", "@tamagui/config": "1.132.20", "@tamagui/core": "1.132.20", "@tamagui/input": "1.132.20", "@tamagui/lucide-icons": "1.132.20", "@tamagui/portal": "1.132.20", "@tamagui/radio-group": "1.132.20", "@tamagui/sheet": "1.132.20", "@tamagui/stacks": "1.132.20", "@tamagui/text": "1.132.20", "@tamagui/toast": "1.132.20", "diff": "8.0.2", "react": "19.0.0", "react-dom": "19.0.0", "react-router-dom": "^6.28.0", "react-syntax-highlighter": "15.6.1", "tamagui": "1.132.20"}, "devDependencies": {"@tamagui/vite-plugin": "^1.132.20", "@types/react": "~19.0.10", "@types/react-dom": "^19.0.2", "@types/react-syntax-highlighter": "15.5.13", "@typescript-eslint/eslint-plugin": "^8.15.0", "@typescript-eslint/parser": "^8.15.0", "@vitejs/plugin-react": "^4.3.4", "eslint": "^9.25.0", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.16", "prettier": "3.6.2", "typescript": "~5.8.3", "vite": "^6.0.3"}, "private": true}