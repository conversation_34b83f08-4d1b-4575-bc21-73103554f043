import { useCallback } from 'react'

export const useCopyToClipboard = () => {
  const copyToClipboard = useCallback(async (text: string) => {
    try {
      // Use the modern Clipboard API for web
      if (navigator.clipboard && window.isSecureContext) {
        await navigator.clipboard.writeText(text)
      } else {
        // Fallback for older browsers or non-secure contexts
        const textArea = document.createElement('textarea')
        textArea.value = text
        textArea.style.position = 'fixed'
        textArea.style.left = '-999999px'
        textArea.style.top = '-999999px'
        document.body.appendChild(textArea)
        textArea.focus()
        textArea.select()
        document.execCommand('copy')
        textArea.remove()
      }

      // TODO: Add toast notification when toast system is available
      console.log('Copied to clipboard:', text.substring(0, 50) + '...')

      return true
    } catch (error) {
      console.error('Failed to copy to clipboard:', error)
      return false
    }
  }, [])

  return { copyToClipboard }
}
