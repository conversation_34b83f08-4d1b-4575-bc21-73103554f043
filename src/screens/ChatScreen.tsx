import { <PERSON><PERSON><PERSON>, LegendListRef } from '@legendapp/list'
import { useSelector } from '@legendapp/state/react'
import type { SessionMessageResponse } from '@opencode-ai/sdk'
import { ChevronDown } from '@tamagui/lucide-icons'
import { useCallback, useEffect, useMemo, useRef, useState } from 'react'
import { useNavigate, useParams } from 'react-router-dom'
import { Button, Text, YStack } from 'tamagui'
import { InputBar } from '../components/chat/InputBar'
import { MessageBubble } from '../components/chat/MessageBubble'
import { Header } from '../components/ui/Header'
import { SessionActionsButton } from '../components/ui/SessionActionsButton'
import { MessageSkeleton } from '../components/ui/SkeletonLoader'
import { store$ } from '../store'
import { actions } from '../store/actions'
import {
    currentMessages,
    currentSession,
    isConnected,
    isSendingMessage,
    selectedAgent,
    selectedModel,
} from '../store/computed'
import { debug } from '../utils/debug'

export default function ChatScreen() {
  const { id } = useParams<{ id: string }>()
  const navigate = useNavigate()
  const listRef = useRef<LegendListRef>(null)
  const [inputValue, setInputValue] = useState('')
  const [showScrollButton, setShowScrollButton] = useState(false)
  const [isNearBottom, setIsNearBottom] = useState(true)
  const prevMessagesLength = useRef(0)

  // LegendState integration
  const connected = useSelector(isConnected)
  const messages = useSelector(currentMessages)
  const session = useSelector(currentSession)
  const model = useSelector(selectedModel)
  const currentAgent = useSelector(selectedAgent)
  const isSending = useSelector(isSendingMessage)
  const isAborting = useSelector(store$.messages.isAborting)
  const isLoading = useSelector(store$.messages.isLoading)

  // Fix: isStreaming should be false when aborting to prevent button state issues
  const isStreaming = isSending && !isAborting

  // Use CSS media query for responsive design
  const isTablet = window.innerWidth > 768

  // Sort all messages chronologically by timestamp
  const sortedMessages = useMemo(() => {
    return messages.sort((a, b) => {
      const timeA = a.info.time.created
      const timeB = b.info.time.created

      // Handle placeholder timestamps (epoch time) - keep streaming messages at end
      if (timeA === 0) return 1
      if (timeB === 0) return -1

      return timeA - timeB
    })
  }, [messages])
  const scrollToBottom = useCallback(() => {
    listRef.current?.scrollToEnd({ animated: true })
  }, [])

  // Set current session and load messages when id changes
  useEffect(() => {
    if (id) {
      // Load messages asynchronously without blocking UI
      actions.messages.loadMessages(id).catch(error => {
        console.error('Failed to load messages:', error)
      })
    }
  }, [id])

  // Auto-scroll when new assistant messages arrive (only if user was already near bottom)
  useEffect(() => {
    // Only auto-scroll if new messages were added
    if (
      sortedMessages.length > prevMessagesLength.current &&
      sortedMessages.length > 0
    ) {
      const lastMessage = sortedMessages[sortedMessages.length - 1]
      // Capture isNearBottom at the time of message addition
      const wasNearBottom = isNearBottom
      if (lastMessage.info.role === 'assistant' && wasNearBottom) {
        setTimeout(() => scrollToBottom(), 100)
      }
      prevMessagesLength.current = sortedMessages.length
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [sortedMessages, scrollToBottom])

  // Auto-scroll during streaming if user is near bottom
  useEffect(() => {
    const hasStreamingMessage = sortedMessages.some(
      m => m.info.role === 'assistant' && !('completed' in m.info.time)
    )

    if (hasStreamingMessage && isNearBottom) {
      // Smooth scroll during streaming
      const scrollInterval = setInterval(() => {
        scrollToBottom()
      }, 200)

      return () => clearInterval(scrollInterval)
    }
  }, [sortedMessages, isNearBottom, scrollToBottom])

  const handleScroll = useCallback(
    (event: {
      nativeEvent: {
        contentOffset: { y: number }
        contentSize: { height: number }
        layoutMeasurement: { height: number }
      }
    }) => {
      const { contentOffset, contentSize, layoutMeasurement } =
        event.nativeEvent
      const isScrollable = contentSize.height > layoutMeasurement.height
      const distanceFromBottom =
        contentSize.height - (contentOffset.y + layoutMeasurement.height)
      const nearBottom = distanceFromBottom < 100

      setIsNearBottom(nearBottom)
      const shouldShow = isScrollable && !nearBottom

      if (shouldShow !== showScrollButton) {
        setShowScrollButton(shouldShow)
        Animated.timing(scrollButtonOpacity, {
          toValue: shouldShow ? 1 : 0,
          duration: 200,
          useNativeDriver: true,
        }).start()
      }
    },
    [showScrollButton, scrollButtonOpacity]
  )

  const handleSendMessage = async () => {
    if (!inputValue.trim() || !id || !model) return

    const messageContent = inputValue.trim()
    setInputValue('')

    // Auto-scroll when user sends a message
    setTimeout(() => scrollToBottom(), 100)

    try {
      const currentSelection = store$.models.selected.get()
      const providerId = currentSelection?.providerID || 'anthropic'
      await actions.messages.sendMessage(
        id,
        messageContent,
        model.id,
        providerId,
        currentAgent?.name || 'build'
      )
    } catch (err) {
      console.error('Failed to send message:', err)
      // Error handling is managed by store actions
    }
  }

  const handleStopStreaming = async () => {
    if (!id) return

    // Check if already aborting to prevent duplicates
    const isAborting = store$.messages.isAborting.get()
    if (isAborting) return

    try {
      debug.log('Stop streaming requested for session:', id)
      await actions.messages.abortSession(id)
      debug.success('Session aborted successfully')
    } catch (error) {
      debug.error('Failed to abort session:', error)
      // Error handling is managed by store actions
    }
  }

  const handleModelSelect = (modelId: string) => {
    // Find which provider owns this model
    const providers = store$.models.providers.get()
    let foundProviderId: string | null = null

    for (const provider of providers) {
      if (provider.models && provider.models[modelId]) {
        foundProviderId = provider.id
        break
      }
    }

    // If we found the provider, use it; otherwise fall back to current or default
    if (foundProviderId) {
      actions.models.selectModel(modelId, foundProviderId)
    } else {
      // This shouldn't happen if the model selector is working correctly
      debug.warn(`Could not find provider for model ${modelId}`)
      const currentSelection = store$.models.selected.get()
      const providerId = currentSelection?.providerID || 'anthropic'
      actions.models.selectModel(modelId, providerId)
    }
  }

  const handleShareSession = async (): Promise<void> => {
    if (!id) return
    try {
      await actions.sessions.shareSession(id)
    } catch (error) {
      debug.error('Failed to share session:', error)
    }
  }

  const handleUnshareSession = async (): Promise<void> => {
    if (!id) return
    try {
      await actions.sessions.unshareSession(id)
    } catch (error) {
      debug.error('Failed to unshare session:', error)
    }
  }

  const handleDeleteSession = async (): Promise<void> => {
    if (!id) return
    try {
      await actions.sessions.deleteSession(id)
      // Navigate back to session list after successful deletion
      navigate('/sessions')
    } catch (error) {
      debug.error('Failed to delete session:', error)
    }
  }

  const renderMessage = ({ item }: { item: SessionMessageResponse }) => (
    <MessageBubble message={item} />
  )

  return (
    <YStack
      flex={1}
      backgroundColor="$background"
      padding="$4"
    >
        {/* Header */}
        <Header
          title={session?.title || 'Chat'}
          showBackButton={true}
          onBackPress={() => navigate('/sessions')}
          connected={connected}
          rightContent={
            session ? (
              <SessionActionsButton
                sessionId={session.id}
                sessionTitle={session.title}
                isShared={!!session.share}
                shareUrl={session.share?.url}
                onShare={handleShareSession}
                onUnshare={handleUnshareSession}
                onDelete={handleDeleteSession}
                isLoading={store$.sessions.isLoading.get()}
              />
            ) : undefined
          }
        />

        {/* Messages */}
        <YStack
          flex={1}
          maxWidth={isTablet ? 1200 : undefined}
          alignSelf="center"
          width="100%"
        >
          {isLoading ? (
            <MessageSkeleton />
          ) : sortedMessages.length === 0 ? (
            <YStack
              flex={1}
              justifyContent="center"
              alignItems="center"
              gap="$4"
              padding="$4"
            >
              <Text
                fontSize={isTablet ? '$7' : '$6'}
                fontWeight="600"
                color="$color"
              >
                Start a session
              </Text>
              <Text
                fontSize={isTablet ? '$5' : '$4'}
                color="$color11"
                textAlign="center"
                maxWidth={400}
              >
                Type a message below to begin chatting with{' '}
                {model?.name || 'AI'}
              </Text>
            </YStack>
          ) : (
            <LegendList
              ref={listRef}
              data={sortedMessages}
              renderItem={renderMessage}
              keyExtractor={item => item.info.id}
              showsVerticalScrollIndicator={false}
              onScroll={handleScroll}
              style={{ flex: 1 }}
              contentContainerStyle={{
                paddingVertical: 20,
                maxWidth: isTablet ? 800 : undefined,
                alignSelf: isTablet ? 'center' : undefined,
                width: isTablet ? '100%' : undefined,
              }}
            />
          )}
        </YStack>

        {/* Scroll to Bottom Button */}
        {showScrollButton && (
          <YStack
            position="absolute"
            bottom={isTablet ? 170 : 150}
            alignSelf="center"
            zIndex={10}
          >
            <Button
              width={48}
              height={48}
              borderRadius={24}
              backgroundColor="rgba(0, 0, 0, 0.7)"
              icon={ChevronDown}
              scaleIcon={1.2}
              color="white"
              onPress={scrollToBottom}
              pressStyle={{
                scale: 0.9,
                backgroundColor: 'rgba(0, 0, 0, 0.8)',
              }}
              shadowColor="$shadowColor"
              shadowOffset={{ width: 0, height: 2 }}
              shadowOpacity={0.25}
              shadowRadius={4}
              elevation={5}
            />
          </YStack>
        )}

        {/* Input Bar */}
        <YStack
          maxWidth={isTablet ? 1200 : undefined}
          alignSelf="center"
          width="100%"
          padding={isTablet ? '$6' : '$4'}
          marginHorizontal={1}

          backgroundColor="$backgroundHover"
          borderColor="$borderColor"
          borderTopWidth={0.5}
          borderLeftWidth={0.5}
          borderRightWidth={0.5}
          borderTopLeftRadius="$6"
          borderTopRightRadius="$6"
        >
          <InputBar
            value={inputValue}
            onChange={setInputValue}
            onSubmit={handleSendMessage}
            onStop={handleStopStreaming}
            onModelSelect={handleModelSelect}
            isStreaming={isStreaming}
            isAborting={isAborting}
            currentModel={model?.id || ''}
            placeholder="Type a message..."
          />
        </YStack>
      </YStack>
  )
}
