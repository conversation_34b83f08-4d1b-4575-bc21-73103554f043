import { useSelector } from '@legendapp/state/react'
import React, { useEffect, useState } from 'react'
import { useNavigate } from 'react-router-dom'
import { LoadingScreen } from '../components/ui/LoadingScreen'
import { APP_CONFIG } from '../config/constants'
import { store$ } from '../store'

export default function IndexScreen() {
  const navigate = useNavigate()
  const [isNavigating, setIsNavigating] = useState(false)
  const serverUrl = useSelector(store$.connection.serverUrl)

  useEffect(() => {
    // Add a small delay to ensure navigation is ready
    const timer = setTimeout(() => {
      if (isNavigating) return // Prevent multiple navigations

      setIsNavigating(true)

      // Check if user has a server URL configured
      if (serverUrl) {
        // Navigate to sessions if already configured
        navigate('/sessions', { replace: true })
      } else {
        // Navigate to connection screen for first-time setup
        navigate('/connection', { replace: true })
      }
    }, 100) // Small delay to ensure navigation is mounted

    return () => clearTimeout(timer)
  }, [navigate, isNavigating, serverUrl])

  return <LoadingScreen message={`Initializing ${APP_CONFIG.name}...`} />
}
