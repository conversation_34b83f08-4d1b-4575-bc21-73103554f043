import { useTheme } from '@tamagui/core'
import { useEffect } from 'react'

export function ThemeStatusBar() {
  const theme = useTheme()

  useEffect(() => {
    // Set the theme color meta tag for browsers
    const metaThemeColor = document.querySelector('meta[name="theme-color"]')
    if (metaThemeColor) {
      metaThemeColor.setAttribute('content', theme.background.val)
    } else {
      const meta = document.createElement('meta')
      meta.name = 'theme-color'
      meta.content = theme.background.val
      document.head.appendChild(meta)
    }

    // Set the background color of the body
    document.body.style.backgroundColor = theme.background.val
  }, [theme.background.val])

  // This component doesn't render anything visible on web
  return null
}
