import React from 'react'
import { Heading, YStack } from 'tamagui'

export interface SectionHeaderProps {
  title: string
  isTablet?: boolean
}

export function SectionHeader({ title, isTablet }: SectionHeaderProps) {
  // Use CSS media query for responsive design
  const isTabletScreen = isTablet ?? window.innerWidth > 768

  return (
    <YStack
      paddingHorizontal="$3"
      paddingTop="$4"
      paddingBottom="$2"
      justifyContent="center"
    >
      <Heading
        size={isTabletScreen ? '$6' : '$5'}
        color="$color11"
        fontWeight="600"
      >
        {title}
      </Heading>
    </YStack>
  )
}
