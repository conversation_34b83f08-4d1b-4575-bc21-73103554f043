import { useSelector } from '@legendapp/state/react'
import { TamaguiProvider } from '@tamagui/core'
import { PortalProvider } from '@tamagui/portal'
import { useEffect } from 'react'
import { Routes, Route, Navigate } from 'react-router-dom'
import { ThemeStatusBar } from './components/ui/ThemeStatusBar'
import { store$ } from './store'
import { actions } from './store/actions'
import config from '../tamagui.config'
// Import sync service to initialize it
import './store/sync'

// Import screens
import IndexScreen from './screens/IndexScreen'
import ConnectionScreen from './screens/ConnectionScreen'
import SessionListScreen from './screens/SessionListScreen'
import ChatScreen from './screens/ChatScreen'

function AppContent() {
  const currentTheme = useSelector(store$.theme)

  // Initialize connection from storage on app start
  useEffect(() => {
    actions.connection.initializeFromStorage()
  }, [])

  return (
    <TamaguiProvider config={config} defaultTheme={currentTheme}>
      <ThemeStatusBar />
      <PortalProvider shouldAddRootHost>
        <Routes>
          <Route path="/" element={<IndexScreen />} />
          <Route path="/connection" element={<ConnectionScreen />} />
          <Route path="/sessions" element={<SessionListScreen />} />
          <Route path="/chat/:id" element={<ChatScreen />} />
          <Route path="*" element={<Navigate to="/" replace />} />
        </Routes>
      </PortalProvider>
    </TamaguiProvider>
  )
}

export default function App() {
  return <AppContent />
}
