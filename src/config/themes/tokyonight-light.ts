export const tokyoNightLight = {
  // Backgrounds
  background: '#e6e7ed',
  backgroundHover: '#d6d7dd',
  backgroundPress: '#c6c7cd',
  backgroundStrong: '#f0f1f7',
  backgroundTransparent: 'rgba(0, 0, 0, 0.3)',

  // Borders & Separators
  borderColor: '#6c6e75',
  borderColorHover: '#5a5c63',
  borderColorPress: '#484a51',

  // Text Colors
  color: '#343b58', // Primary text
  color11: '#6c6e75', // Secondary text/comments
  color12: '#40434f', // Tertiary text
  placeholderColor: '#6c6e75', // Placeholder text

  // Accent Colors
  blue10: '#2959aa', // Build mode, functions
  blue11: '#1e4088', // Darker blue variant
  orange10: '#965027', // Plan mode, numbers
  orange11: '#7a3f1f', // Darker orange variant
  green10: '#385f0d', // Success, active status
  green11: '#2d4a0a', // Darker green variant
  red10: '#8c4351', // Error status
  red11: '#73363f', // Darker red variant
  yellow10: '#8f5e15', // Warning, idle status
  yellow11: '#734b11', // Darker yellow variant
  purple10: '#5a3e8e', // Focus states
  purple11: '#4a3373', // Darker purple variant

  // Special Colors
  cyan10: '#0f4b6e', // Info, links
  cyan11: '#0c3d5a', // Darker cyan variant

  // Shadows
  shadowColor: 'rgba(0, 0, 0, 0.1)',
  shadowColorStrong: 'rgba(0, 0, 0, 0.2)',
}
