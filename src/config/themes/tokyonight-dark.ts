export const tokyoNightDark = {
  // Backgrounds
  background: '#1a1b26',
  backgroundHover: '#24283b',
  backgroundPress: '#292e42',
  backgroundStrong: '#16161e',
  backgroundTransparent: 'rgba(0, 0, 0, 0.5)',

  // Borders & Separators
  borderColor: '#414868',
  borderColorHover: '#565f89',
  borderColorPress: '#6183bb',

  // Text Colors
  color: '#c0caf5', // Primary text
  color11: '#565f89', // Secondary text/comments
  color12: '#a9b1d6', // Tertiary text
  placeholderColor: '#565f89', // Placeholder text

  // Accent Colors
  blue10: '#7aa2f7', // Build mode, functions
  blue11: '#6183bb', // Darker blue variant
  orange10: '#ff9e64', // Plan mode, numbers
  orange11: '#e0823d', // Darker orange variant
  green10: '#9ece6a', // Success, active status
  green11: '#7fb069', // Darker green variant
  red10: '#f7768e', // Error status
  red11: '#e85d75', // Darker red variant
  yellow10: '#e0af68', // Warning, idle status
  yellow11: '#c49a61', // Darker yellow variant
  purple10: '#bb9af7', // Focus states
  purple11: '#a78bfa', // Darker purple variant

  // Special Colors
  cyan10: '#7dcfff', // Info, links
  cyan11: '#6bb6ff', // Darker cyan variant

  // Shadows
  shadowColor: 'rgba(0, 0, 0, 0.5)',
  shadowColorStrong: 'rgba(0, 0, 0, 0.8)',
}
