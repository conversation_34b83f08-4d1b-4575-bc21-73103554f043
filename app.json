{"expo": {"name": "Opencode", "slug": "opencode", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/images/icon.png", "scheme": "opencode", "userInterfaceStyle": "automatic", "newArchEnabled": true, "ios": {"supportsTablet": true, "bundleIdentifier": "dev.drnkn.opencode", "infoPlist": {"ITSAppUsesNonExemptEncryption": false}}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/images/adaptive-icon.png", "backgroundColor": "#ffffff"}, "edgeToEdgeEnabled": true, "package": "dev.drnkn.opencode"}, "web": {"bundler": "metro", "output": "static", "favicon": "./assets/images/favicon.png"}, "plugins": ["expo-router", ["expo-splash-screen", {"image": "./assets/images/splash-icon.png", "imageWidth": 200, "resizeMode": "contain", "backgroundColor": "#ffffff"}]], "experiments": {"typedRoutes": true}, "extra": {"router": {}, "debug": true, "eas": {"projectId": "dda90611-9db0-4797-9103-0110a1d915f6"}}}}